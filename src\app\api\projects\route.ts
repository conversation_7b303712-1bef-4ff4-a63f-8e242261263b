import { NextRequest, NextResponse } from 'next/server'
import { getPayload } from 'payload'
import config from '@payload-config'

export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config })

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const category = searchParams.get('category')
    const featured = searchParams.get('featured')
    const status = searchParams.get('status')

    // Build where clause
    const where = {} as any
    if (category) where.category = { equals: category }
    if (featured === 'true') where.featured = { equals: true }
    if (status) where.status = { equals: status }

    // Fetch projects directly from PayloadCMS
    const result = await payload.find({
      collection: 'projects',
      where: Object.keys(where).length > 0 ? where : undefined,
      page,
      limit,
      sort: '-createdAt',
    })

    return NextResponse.json({
      projects: result.docs,
      totalProjects: result.totalDocs,
      page: result.page,
      limit: result.limit,
      totalPages: result.totalPages,
      hasNextPage: result.hasNextPage,
      hasPrevPage: result.hasPrevPage,
    })
  } catch (error) {
    console.error('Projects API error:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 },
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const payload = await getPayload({ config })

    // Parse request body with better error handling
    let body
    try {
      const rawBody = await request.text()
      console.log('Raw request body:', rawBody)
      body = JSON.parse(rawBody)
    } catch (parseError) {
      console.error('JSON parsing error:', parseError)
      return NextResponse.json(
        {
          error: 'Invalid JSON',
          message: 'Request body must be valid JSON',
        },
        { status: 400 },
      )
    }

    // Validate required fields (image is optional for testing)
    const requiredFields = ['title', 'summary', 'category', 'pillar']
    const missingFields = requiredFields.filter((field) => !body[field])

    if (missingFields.length > 0) {
      return NextResponse.json(
        {
          error: 'Validation error',
          message: `Missing required fields: ${missingFields.join(', ')}`,
        },
        { status: 400 },
      )
    }

    // Validate enum values
    const validCategories = [
      'knowledge-preservation',
      'community-empowerment',
      'capacity-building',
      'research-development',
      'policy-advocacy',
      'market-development',
      'technology-innovation',
    ]

    const validPillars = [
      'indigenous-knowledge',
      'community-innovation',
      'capacity-building',
      'market-development',
      'policy-framework',
    ]

    const validStatuses = ['planning', 'active', 'completed', 'on-hold', 'cancelled']

    if (!validCategories.includes(body.category)) {
      return NextResponse.json(
        {
          error: 'Validation error',
          message: `Invalid category. Must be one of: ${validCategories.join(', ')}`,
        },
        { status: 400 },
      )
    }

    if (!validPillars.includes(body.pillar)) {
      return NextResponse.json(
        {
          error: 'Validation error',
          message: `Invalid pillar. Must be one of: ${validPillars.join(', ')}`,
        },
        { status: 400 },
      )
    }

    if (body.status && !validStatuses.includes(body.status)) {
      return NextResponse.json(
        {
          error: 'Validation error',
          message: `Invalid status. Must be one of: ${validStatuses.join(', ')}`,
        },
        { status: 400 },
      )
    }

    // Prepare data for creation
    const projectData = {
      ...body,
      // Set default values for optional fields
      status: body.status || 'active',
      featured: body.featured || false,
      published: body.published !== false, // Default to true unless explicitly false
    }

    // Remove image field if it's not a valid ObjectId (for testing purposes)
    if (projectData.image && typeof projectData.image === 'string') {
      // Simple check for ObjectId format (24 hex characters)
      if (!/^[0-9a-fA-F]{24}$/.test(projectData.image)) {
        console.log('Invalid image ID provided, removing from data:', projectData.image)
        delete projectData.image
      }
    }

    // Create new project
    const result = await payload.create({
      collection: 'projects',
      data: projectData,
    })

    return NextResponse.json(
      {
        success: true,
        message: 'Project created successfully',
        project: result,
      },
      { status: 201 },
    )
  } catch (error) {
    console.error('Projects POST error:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Failed to create project',
      },
      { status: 500 },
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const body = await request.json()
    const { id, ...updateData } = body

    if (!id) {
      return NextResponse.json(
        {
          error: 'Validation error',
          message: 'Project ID is required for updates',
        },
        { status: 400 },
      )
    }

    // Update project
    const result = await payload.update({
      collection: 'projects',
      id,
      data: updateData,
    })

    return NextResponse.json({
      success: true,
      message: 'Project updated successfully',
      project: result,
    })
  } catch (error) {
    console.error('Projects PUT error:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Failed to update project',
      },
      { status: 500 },
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const payload = await getPayload({ config })
    const searchParams = request.nextUrl.searchParams
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        {
          error: 'Validation error',
          message: 'Project ID is required for deletion',
        },
        { status: 400 },
      )
    }

    // Delete project
    await payload.delete({
      collection: 'projects',
      id,
    })

    return NextResponse.json({
      success: true,
      message: 'Project deleted successfully',
    })
  } catch (error) {
    console.error('Projects DELETE error:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Failed to delete project',
      },
      { status: 500 },
    )
  }
}
